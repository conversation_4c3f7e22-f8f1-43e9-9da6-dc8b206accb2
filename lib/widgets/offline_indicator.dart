import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/providers/chat_provider.dart';
import 'package:tolk/utils/app_colors.dart';

class OfflineIndicator extends StatefulWidget {
  final bool showDetails;

  const OfflineIndicator({super.key, this.showDetails = false});

  @override
  State<OfflineIndicator> createState() => _OfflineIndicatorState();
}

class _OfflineIndicatorState extends State<OfflineIndicator>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        final isOnline = chatProvider.isOnline;
        final isSyncing = chatProvider.isSyncing;
        final unsyncedCount = chatProvider.getUnsyncedMessageCount();

        if (isOnline && !isSyncing && unsyncedCount == 0) {
          // Everything is synced and online - no indicator needed
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getBackgroundColor(isOnline, isSyncing),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Status icon
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: isSyncing ? _pulseAnimation.value : 1.0,
                    child: Icon(
                      _getStatusIcon(isOnline, isSyncing),
                      color: Colors.white,
                      size: 16,
                    ),
                  );
                },
              ),
              const SizedBox(width: 6),
              // Status text

              // Details button
              if (widget.showDetails && (unsyncedCount > 0 || !isOnline)) ...[
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () => _showSyncDetails(context, chatProvider),
                  child: const Icon(
                    Icons.info_outline,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Color _getBackgroundColor(bool isOnline, bool isSyncing) {
    if (!isOnline) {
      return Colors.red.shade600;
    } else if (isSyncing) {
      return Colors.orange.shade600;
    } else {
      return AppColors.appColor;
    }
  }

  IconData _getStatusIcon(bool isOnline, bool isSyncing) {
    if (!isOnline) {
      return Icons.cloud_off;
    } else if (isSyncing) {
      return Icons.sync;
    } else {
      return Icons.cloud_done;
    }
  }

  String _getStatusText(bool isOnline, bool isSyncing, int unsyncedCount) {
    if (!isOnline) {
      if (unsyncedCount > 0) {
        return 'Offline • $unsyncedCount pending';
      }
      return 'Offline';
    } else if (isSyncing) {
      return 'Syncing...';
    } else if (unsyncedCount > 0) {
      return '$unsyncedCount pending';
    } else {
      return 'Synced';
    }
  }

  void _showSyncDetails(BuildContext context, ChatProvider chatProvider) {
    final stats = chatProvider.getSyncStats();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.splashColor,
            title: const Text(
              'Sync Status',
              style: TextStyle(color: Colors.white),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatRow(
                  'Connection',
                  stats['isOnline'] ? 'Online' : 'Offline',
                ),
                _buildStatRow(
                  'Sync Status',
                  stats['isSyncing'] ? 'Syncing' : 'Idle',
                ),
                _buildStatRow(
                  'Unsynced Messages',
                  '${stats['unsyncedMessages']}',
                ),
                _buildStatRow('Total Messages', '${stats['totalMessages']}'),
                _buildStatRow('Chat Rooms', '${stats['totalChatRooms']}'),
              ],
            ),
            actions: [
              if (chatProvider.isOnline)
                TextButton(
                  onPressed: () {
                    chatProvider.forceSync();
                    Navigator.of(context).pop();
                  },
                  child: const Text(
                    'Force Sync',
                    style: TextStyle(color: AppColors.appColor),
                  ),
                ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'Close',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(color: Colors.white70)),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// Simple offline indicator for chat list
class SimpleOfflineIndicator extends StatelessWidget {
  const SimpleOfflineIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        if (chatProvider.isOnline) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8),
          color: Colors.red.shade600,
          child: Text(
            'You are offline. Messages will sync when connection is restored.',
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        );
      },
    );
  }
}

// Message status indicator for individual messages
class MessageSyncIndicator extends StatelessWidget {
  final bool isSynced;
  final bool isUploading;

  const MessageSyncIndicator({
    super.key,
    required this.isSynced,
    this.isUploading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isSynced && !isUploading) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(left: 4),
      child: Icon(
        isUploading ? Icons.upload : Icons.schedule,
        size: 12,
        color: isUploading ? Colors.orange : Colors.grey,
      ),
    );
  }
}
